package com.polarbear.kd.enums;

import lombok.Getter;

@Getter
public enum KingdeeJobTypeEnum {
  SAVE_MATERIAL("saveMaterial", "保存商品物料"),
  SAVE_PROJECT("saveProject", "保存项目"),
  SAVE_SAL_ORDER("saveSalOrder", "保存销售订单"),
  SAVE_SALOUT_BILL("saveSaloutBill", "保存销售出库退货单"),
  SAVE_PUR_ORDER("savePurOrder", "保存采购订单"),
  SAVE_PRUIN_BILL("savePruinBill", "保存采购入库退料单"),
  SAVE_DEFICIT_BILL("saveDeficitBill", "保存盘亏单"),
  SAVE_SURPLUS_BILL("saveSurplusBill", "保存盘盈单"),
  SAVE_TRANS_OUT_BILL("SaveTransOutBill", "保存分步调出单"),
  SAVE_TRANS_IN_BILL("batchAdd_V2", "保存分步调入单");

  private final String code;
  private final String description;

  KingdeeJobTypeEnum(String code, String description) {
    this.code = code;
    this.description = description;
  }
}
