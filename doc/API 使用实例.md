2240平方千米84万
## 基本使用流程  
  
### 1. 创建请求对象  
```java  
// 创建供应商保存请求  
SupplierSaveRequest request = new SupplierSaveRequest();  
  
// 设置请求参数  
SupplierSavePara para = new SupplierSavePara();  
para.setNumber("SUP001");  
para.setName("测试供应商");  
para.setGjwlAppid("app123");  
  
request.setParam(para);  
```  
  
### 2. 设置日志客户端（可选）  
```java  
SdkLogClient logClient = new SdkLogClientImpl();  
request.sdkLogClient(logClient);  
```  
  
### 3. 执行API调用  
  
#### 方式一：通过KdClinet获取Token  
```java  
KingdeeApiBaseClient api = new KdClinetImpl();  
SupplierSaveResponse response = request.execute(api);  
```  
  
#### 方式二：直接使用AccessToken  
```java  
KdAccessTokenDTO accessToken = getAccessToken(); // 获取访问令牌  
SupplierSaveResponse response = request.execute(accessToken);  
```  
  
### 4. 处理响应结果  
```java  
if (response != null) {  
    response.check(); // 检查业务响应码  
    // 处理业务数据  
    System.out.println("操作成功");  
} else {  
    System.out.println("租户未使用金蝶系统");  
}  
```  
  
## 具体API使用示例  
  
### 客户管理示例  
  
#### 保存客户信息  
```java  
// 创建客户保存请求  
CustomerSaveRequest request = new CustomerSaveRequest();  
  
// 创建客户信息列表  
List<Customer> customers = new ArrayList<>();  
Customer customer = new Customer();  
customer.setNumber("CUST001");  
customer.setName("测试客户");  
customers.add(customer);  
  
request.setParam(customers);  
  
// 执行请求  
CustomerSaveResponse response = request.execute(api);  
response.check();  
```  
  
#### 客户反审核  
```java  
CustomerUnAuditRequest request = new CustomerUnAuditRequest();  
  
CustomerUnAudit unAudit = new CustomerUnAudit();  
unAudit.setCustomerId("123456");  
unAudit.setReason("测试反审核");  
  
request.setParam(unAudit);  
CustomerUnAuditResponse response = request.execute(api);  
response.check();  
```  
  
### 供应商管理示例  
  
#### 创建供应商  
```java  
SupplierCreateRequest request = new SupplierCreateRequest();  
  
SupplierCreatePara para = new SupplierCreatePara();  
para.setNumber("SUP001");  
para.setName("测试供应商");  
  
// 设置联系人信息  
List<Linkman> linkmen = new ArrayList<>();  
Linkman linkman = new Linkman();  
linkman.setName("张三");  
linkman.setPhone("***********");  
linkmen.add(linkman);  
para.setEntryLinkman(linkmen);  
  
// 设置银行信息  
List<BankInfo> banks = new ArrayList<>();  
BankInfo bank = new BankInfo();  
bank.setBankName("中国银行");  
bank.setAccountNumber("**********");  
banks.add(bank);  
para.setEntryBank(banks);  
  
request.setParam(para);  
SimpleResponse response = request.execute(api);  
response.check();  
```  
  
#### 批量更新供应商  
```java  
SupplierUpdateRequest request = new SupplierUpdateRequest();  
  
List<SupplierUpdatePara> updateList = new ArrayList<>();  
SupplierUpdatePara updatePara = new SupplierUpdatePara();  
updatePara.setNumber("SUP001");  
updatePara.setName("更新后的供应商名称");  
updateList.add(updatePara);  
  
request.setParam(updateList);  
SimpleResponse response = request.execute(api);  
response.check();  
```  
  
### 收款管理示例  
  
#### 新增收款单  
```java  
PaymentRequest request = new PaymentRequest();  
  
List<PaymentPara> payments = new ArrayList<>();  
PaymentPara payment = new PaymentPara();  
payment.setBillNo("REC001");  
payment.setBizDate(LocalDate.now());  
payment.setPayerType("bd_customer");  
payment.setTxtDescription("收款测试");  
payment.setActRecAmt(new BigDecimal("1000.00"));  
payment.setExchangeRate(new BigDecimal("1.0"));  
payment.setLocalAmt(new BigDecimal("1000.00"));  
payment.setPayerName("测试客户");  
  
payments.add(payment);  
request.setParam(payments);  
  
PaymentResponse response = request.execute(api);  
response.check();  
```  
  
#### 查询收款流水  
```java  
ReceiptRecordQueryParams params = new ReceiptRecordQueryParams();  
params.setCompCode("001");  
  
ReceiptRecordQueryRequest request = new ReceiptRecordQueryRequest(params);  
DetailQueryResponse response = request.execute(api);  
response.check();  
```  
  
#### 收款流水认领  
```java  
ReceiptClaimParams params = new ReceiptClaimParams();  
params.setCompCode("001");  
  
ReceiptClaimItem claimItem = new ReceiptClaimItem();  
claimItem.setReceivablesId(123456L);  
claimItem.setReceivablesNum("REC001");  
params.setClaimRevInforBean(claimItem);  
  
ReceiptClaimParamsRequest request = new ReceiptClaimParamsRequest(params);  
ReceiptClaimResponse response = request.execute(api);  
response.check();  
```  
  
#### 取消认领  
```java  
CancelClaimParams params = new CancelClaimParams();  
params.setCompCode("001");  
params.setOperateType("UNCLAIM");  
params.setReceivablesId(123456L);  
params.setRemark("测试取消认领");  
params.setCasRecBillId("BILL001");  
  
CancelClaimRequest request = new CancelClaimRequest(params);  
ReceiptClaimResponse response = request.execute(api);  
response.check();  
```  
  
### 付款管理示例  
  
#### 查询付款申请  
```java  
PayApplyQueryRequest request = new PayApplyQueryRequest();  
  
PayApplyQueryParam param = new PayApplyQueryParam();  
// 设置查询条件  
request.setParam(param);  
  
PayApplyQueryResponse response = request.execute(api);  
response.check();  
```  
  
#### 自动创建付款申请  
```java  
PayApplyAutoCreateRequest request = new PayApplyAutoCreateRequest();  
  
List<Claim> claims = new ArrayList<>();  
Claim claim = new Claim();  
// 设置报账单信息  
claims.add(claim);  
  
request.setParam(claims);  
PayApplyAutoCreateResponse response = request.execute(api);  
response.check();  
```  
  
#### 新增金蝶付款单  
```java  
AddKdPaymentRequest request = new AddKdPaymentRequest();  
  
List<AddKdPaymentPara> payments = new ArrayList<>();  
AddKdPaymentPara payment = new AddKdPaymentPara();  
payment.setBillno("PAY001");  
payment.setBizdate(new Date());  
payment.setPayeetype("bd_supplier");  
payment.setDescription("付款测试");  
  
payments.add(payment);  
request.setParam(payments);  
  
AddKdPaymentResponse response = request.execute(api);  
response.check();  
```  
  
#### 查询报账单状态  
```java  
ClaimQueryPara para = new ClaimQueryPara();  
para.setClaimNos("CLAIM001,CLAIM002");  
  
ClaimQueryRequest request = new ClaimQueryRequest();  
request.setParam(para);  
  
PayLineResultResponse response = request.execute(api);
response.check();
```

### 采购订单管理示例

#### 保存采购订单
```java
// 创建采购订单保存请求
PurchaseOrderSaveRequest request = new PurchaseOrderSaveRequest();

// 创建采购订单列表
List<PurchaseOrder> purchaseOrders = new ArrayList<>();
PurchaseOrder purchaseOrder = new PurchaseOrder();

// 设置采购订单主体信息
purchaseOrder.setBillno("CGDD-250911-000004");
purchaseOrder.setOrgNumber("1011");
purchaseOrder.setBilltypeNumber("pm_PurOrderBill_STD_BT_S");
purchaseOrder.setBiztypeNumber("110");
purchaseOrder.setDeptNumber("GJEY1102");
purchaseOrder.setBiztime("2025-09-11");
purchaseOrder.setExratedate("2025-09-11");
purchaseOrder.setSupplierNumber("1111003");
purchaseOrder.setPayconditionNumber("FKTJ-YF-100");
purchaseOrder.setSettlecurrencyNumber("CNY");
purchaseOrder.setComment("备注");
purchaseOrder.setIstax(true);
purchaseOrder.setPaymode("CREDIT");
purchaseOrder.setExratetableNumber("ERT-01");
purchaseOrder.setExchangerate(new BigDecimal("1"));
purchaseOrder.setIspayrate(false);
purchaseOrder.setGjwlThirdpartyBillno("CGDD-250911-000001");
purchaseOrder.setGjwlSourcesystemtype("广交云供应链管理系统");
purchaseOrder.setTrdbillno("CGDD-250911-000001");

// 创建采购订单明细列表
List<PurchaseOrderEntry> entries = new ArrayList<>();
PurchaseOrderEntry entry = new PurchaseOrderEntry();

// 设置采购订单明细信息
entry.setLinetypeNumber("010");
entry.setMaterialNumber("Item-*********");
entry.setUnitNumber("pcs");
entry.setQty(new BigDecimal("100"));
entry.setPrice(new BigDecimal("9.52"));
entry.setPriceandtax(new BigDecimal("10"));
entry.setWarehouseNumber("CK-001");
entry.setDiscounttype("B");
entry.setTaxrateidNumber("V5");
entry.setEntryreqorgNumber("1011");
entry.setEntryrecorgNumber("1011");
entry.setEntrysettleorgNumber("1011");
entry.setOwnertype("bos_org");
entry.setOwnerNumber("1011");
entry.setPromisedate("2025-09-11");
entry.setDeliverdate("2025-09-11");
entry.setIspresent(false);
entry.setProjectNumber("KD-P-ALL_SYS");
entry.setEntrycomment("单据体备注");
entry.setDiscountrate(new BigDecimal("0.10"));
entry.setTaxamount(new BigDecimal("47.14"));
entry.setDiscountamount(new BigDecimal("10.00"));
entry.setAmountandtax(new BigDecimal("990.00"));
entry.setAmount(new BigDecimal("942.86"));

entries.add(entry);
purchaseOrder.setBillentry(entries);

// 创建付款计划列表
List<PurchaseOrderPayEntry> payEntries = new ArrayList<>();
PurchaseOrderPayEntry payEntry = new PurchaseOrderPayEntry();

// 设置付款计划信息
payEntry.setPlanentrysettleorgNumber("1011");
payEntry.setPayrate(new BigDecimal("100.00"));
payEntry.setPayamount(new BigDecimal("990"));
payEntry.setIsprepay(true);

payEntries.add(payEntry);
purchaseOrder.setPurbillentryPay(payEntries);

purchaseOrders.add(purchaseOrder);
request.setParam(purchaseOrders);

// 执行请求
PurchaseOrderSaveResponse response = request.execute(api);
response.check();

// 获取保存结果
PurchaseOrderSaveResponseData data = response.getData();
if (data != null && data.getResult() != null && !data.getResult().isEmpty()) {
    PurchaseOrderSaveResponseData.PurchaseOrderSaveResult result = data.getResult().get(0);
    if (result.getBillStatus()) {
        System.out.println("采购订单保存成功，单据ID: " + result.getId());
        System.out.println("单据编号: " + result.getNumber());
    }
}
```

#### 修改采购订单示例
```java
// 创建采购订单保存请求（修改时需要传入ID）
PurchaseOrderSaveRequest request = new PurchaseOrderSaveRequest();

// 创建采购订单列表
List<PurchaseOrder> purchaseOrders = new ArrayList<>();
PurchaseOrder purchaseOrder = new PurchaseOrder();

// 设置采购订单主体信息（修改时需传入ID）
purchaseOrder.setId("2290027758555811840"); // 修改时需传入采购订单ID
purchaseOrder.setBillno("CGDD-250911-000004");
purchaseOrder.setOrgNumber("1011");
purchaseOrder.setBilltypeNumber("pm_PurOrderBill_STD_BT_S");
purchaseOrder.setBiztypeNumber("110");
purchaseOrder.setDeptNumber("GJEY1102");
purchaseOrder.setBiztime("2025-09-11");
purchaseOrder.setExratedate("2025-09-11");
purchaseOrder.setSupplierNumber("1111003");
purchaseOrder.setPayconditionNumber("FKTJ-YF-100");
purchaseOrder.setSettlecurrencyNumber("CNY");
purchaseOrder.setComment("修改后的备注");
purchaseOrder.setIstax(true);
purchaseOrder.setPaymode("CREDIT");
purchaseOrder.setExratetableNumber("ERT-01");
purchaseOrder.setExchangerate(new BigDecimal("1"));
purchaseOrder.setIspayrate(false);
purchaseOrder.setGjwlThirdpartyBillno("CGDD-250911-000001");
purchaseOrder.setGjwlSourcesystemtype("广交云供应链管理系统");
purchaseOrder.setTrdbillno("CGDD-250911-000001");

// 创建采购订单明细列表（修改时需传入明细ID）
List<PurchaseOrderEntry> entries = new ArrayList<>();
PurchaseOrderEntry entry = new PurchaseOrderEntry();

// 设置采购订单明细信息（修改时需传入明细ID）
entry.setId(3283243790686923776L); // 修改时需传入明细ID
entry.setLinetypeNumber("010");
entry.setMaterialNumber("Item-*********");
entry.setUnitNumber("pcs");
entry.setQty(new BigDecimal("150")); // 修改数量
entry.setPrice(new BigDecimal("9.52"));
entry.setPriceandtax(new BigDecimal("10"));
entry.setWarehouseNumber("CK-001");
entry.setDiscounttype("B");
entry.setTaxrateidNumber("V5");
entry.setEntryreqorgNumber("1011");
entry.setEntryrecorgNumber("1011");
entry.setEntrysettleorgNumber("1011");
entry.setOwnertype("bos_org");
entry.setOwnerNumber("1011");
entry.setPromisedate("2025-09-11");
entry.setDeliverdate("2025-09-11");
entry.setIspresent(false);
entry.setProjectNumber("KD-P-ALL_SYS");
entry.setEntrycomment("修改后的单据体备注");

entries.add(entry);
purchaseOrder.setBillentry(entries);

purchaseOrders.add(purchaseOrder);
request.setParam(purchaseOrders);

// 执行请求
PurchaseOrderSaveResponse response = request.execute(api);
response.check();
```

### 销售订单管理示例

#### 保存销售订单
```java
// 创建销售订单保存请求
SalesOrderSaveRequest request = new SalesOrderSaveRequest();

// 创建销售订单列表
List<SalesOrder> salesOrders = new ArrayList<>();
SalesOrder salesOrder = new SalesOrder();

// 设置销售订单主体信息
salesOrder.setBizdate("2025-08-19");
salesOrder.setOrgNumber("1011");
salesOrder.setCustomerNumber("CUS-001");
salesOrder.setBilltypeNumber("sm_SalesOrder_STD_BT_S");
salesOrder.setBiztypeNumber("210");
salesOrder.setSettlecurrencyNumber("CNY");
salesOrder.setGjwlThirdpartyBillno("XSDD-2025-08-19-001");
salesOrder.setGjwlSourcesystemtype("广交云供应链管理系统");
salesOrder.setComment("测试销售订单");
salesOrder.setIstax(true);
salesOrder.setIswholediscount(false);
salesOrder.setWholediscountamount(BigDecimal.ZERO);
salesOrder.setRecconditionNumber("SKTJ-1001_SYS");

// 创建销售订单明细列表
List<SalesOrderEntry> entries = new ArrayList<>();
SalesOrderEntry entry = new SalesOrderEntry();

// 设置销售订单明细信息
entry.setLinetypeNumber("010");
entry.setMaterialNumber("ITEM-001");
entry.setUnitNumber("pcs");
entry.setEStockorgNumber("1011");
entry.setEntrysettleorgNumber("1011");
entry.setPrice(new BigDecimal("100.00"));
entry.setPriceandtax(new BigDecimal("113.00"));
entry.setQty(new BigDecimal("10"));
entry.setTaxrateidNumber("V5");
entry.setTaxamount(new BigDecimal("13.00"));
entry.setDiscounttype("NULL");
entry.setRemark("测试明细");
entry.setWarehouseNumber("WH-001");
entry.setAmountandtax(new BigDecimal("1130.00"));
entry.setAmount(new BigDecimal("1000.00"));
entry.setIspresent(false);

entries.add(entry);
salesOrder.setBillentry(entries);
salesOrders.add(salesOrder);

request.setParam(salesOrders);

// 执行请求
SalesOrderSaveResponse response = request.execute(api);
response.check();

// 获取保存结果
SalesOrderSaveResponseData data = response.getData();
if (data != null && data.getResult() != null && !data.getResult().isEmpty()) {
    SalesOrderSaveResponseData.SalesOrderSaveResult result = data.getResult().get(0);
    if (result.getBillStatus()) {
        System.out.println("销售订单保存成功，单据ID: " + result.getId());
        System.out.println("单据编号: " + result.getNumber());
    }
}
```

### 销售出库退货单管理示例

#### 保存销售出库单
```java
// 创建销售出库单保存请求
SalesOutBillSaveRequest request = new SalesOutBillSaveRequest();

// 创建销售出库单列表
List<SalesOutBill> salesOutBills = new ArrayList<>();
SalesOutBill salesOutBill = new SalesOutBill();

// 设置销售出库单主体信息
salesOutBill.setOrgNumber("1011");
salesOutBill.setBillno("XSCK-2025-08-19-001");
salesOutBill.setGjwlThirdpartyBillno("GJY-XSCK-001");
salesOutBill.setGjwlSourcesystemtype("广交云供应链管理系统");
salesOutBill.setBilltypeNumber(SalesOutBill.BILLTYPE_SALES_OUT); // 销售出库单
salesOutBill.setBiztypeNumber("210");
salesOutBill.setInvschemeNumber("210");
salesOutBill.setBiztime("2025-08-19");
salesOutBill.setExratedate("2025-08-19");
salesOutBill.setCustomerNumber("CUS-001");
salesOutBill.setPaymode("CREDIT"); // 赊销
salesOutBill.setBizorgNumber("1011");
salesOutBill.setCurrencyNumber("CNY");
salesOutBill.setSettlecurrencyNumber("CNY");
salesOutBill.setExratetableNumber("ERT-01");
salesOutBill.setExchangerate(new BigDecimal("1"));
salesOutBill.setIstax(true);
salesOutBill.setRecconditionNumber("SKTJ-1001_SYS");
salesOutBill.setIswholediscount(false);
salesOutBill.setComment("测试销售出库单");

// 创建销售出库单明细列表
List<SalesOutBillEntry> entries = new ArrayList<>();
SalesOutBillEntry entry = new SalesOutBillEntry();

// 设置销售出库单明细信息
entry.setLinetypeNumber(SalesOutBillEntry.LINETYPE_MATERIAL);
entry.setMaterialNumber("ITEM-001");
entry.setUnitNumber("pcs");
entry.setBaseunitNumber("pcs");
entry.setQty(new BigDecimal("10"));
entry.setBaseqty(new BigDecimal("10"));
entry.setWarehouseNumber("WH-001");
entry.setOutinvtypeNumber(SalesOutBillEntry.OUTINVTYPE_NORMAL);
entry.setOutinvstatusNumber(SalesOutBillEntry.OUTINVSTATUS_NORMAL);
entry.setOutownertype(SalesOutBillEntry.OUTOWNERTYPE_ORG);
entry.setOutownerNumber("1011");
entry.setOutkeepertype(SalesOutBillEntry.OUTKEEPERTYPE_ORG);
entry.setOutkeeperNumber("1011");
entry.setEntrysettleorgNumber("1011");
entry.setPrice(new BigDecimal("100.00"));
entry.setPriceandtax(new BigDecimal("113.00"));
entry.setTaxrateidNumber("V5");
entry.setTaxamount(new BigDecimal("130.00"));
entry.setAmountandtax(new BigDecimal("1130.00"));
entry.setAmount(new BigDecimal("1000.00"));
entry.setIspresent(false);
entry.setDiscounttype(SalesOutBillEntry.DISCOUNTTYPE_NONE);
entry.setEntrycomment("测试明细");

entries.add(entry);
salesOutBill.setBillentry(entries);
salesOutBills.add(salesOutBill);

request.setData(salesOutBills);

// 执行请求
SalesOutBillSaveResponse response = request.execute(api);
response.check();

// 获取保存结果
SalesOutBillSaveResponseData data = response.getData();
if (data != null && data.getResult() != null && !data.getResult().isEmpty()) {
    SalesOutBillSaveResponseData.SalesOutBillSaveResult result = data.getResult().get(0);
    if (result.getBillStatus()) {
        System.out.println("销售出库单保存成功，单据ID: " + result.getId());
        System.out.println("单据编号: " + result.getNumber());
    }
}
```

#### 保存销售退货单
```java
// 创建销售退货单保存请求
SalesOutBillSaveRequest request = new SalesOutBillSaveRequest();

// 创建销售退货单列表
List<SalesOutBill> salesReturnBills = new ArrayList<>();
SalesOutBill salesReturnBill = new SalesOutBill();

// 设置销售退货单主体信息
salesReturnBill.setOrgNumber("1011");
salesReturnBill.setBillno("XSTH-2025-08-19-001");
salesReturnBill.setGjwlThirdpartyBillno("GJY-XSTH-001");
salesReturnBill.setGjwlSourcesystemtype("广交云供应链管理系统");
salesReturnBill.setBilltypeNumber(SalesOutBill.BILLTYPE_SALES_RETURN); // 销售退货单
salesReturnBill.setBiztypeNumber("2101");
salesReturnBill.setInvschemeNumber("2101");
salesReturnBill.setBiztime("2025-08-19");
salesReturnBill.setExratedate("2025-08-19");
salesReturnBill.setCustomerNumber("CUS-001");
salesReturnBill.setPaymode("CASH"); // 现销
salesReturnBill.setBizorgNumber("1011");
salesReturnBill.setCurrencyNumber("CNY");
salesReturnBill.setSettlecurrencyNumber("CNY");
salesReturnBill.setExratetableNumber("ERT-01");
salesReturnBill.setExchangerate(new BigDecimal("1"));
salesReturnBill.setIstax(true);
salesReturnBill.setRecconditionNumber("SKTJ-1001_SYS");
salesReturnBill.setIswholediscount(false);
salesReturnBill.setComment("测试销售退货单");

// 创建销售退货单明细列表
List<SalesOutBillEntry> entries = new ArrayList<>();
SalesOutBillEntry entry = new SalesOutBillEntry();

// 设置销售退货单明细信息
entry.setLinetypeNumber(SalesOutBillEntry.LINETYPE_MATERIAL);
entry.setMaterialNumber("ITEM-001");
entry.setUnitNumber("pcs");
entry.setBaseunitNumber("pcs");
entry.setQty(new BigDecimal("5")); // 退货数量
entry.setBaseqty(new BigDecimal("5"));
entry.setWarehouseNumber("WH-001");
entry.setOutinvtypeNumber(SalesOutBillEntry.OUTINVTYPE_NORMAL);
entry.setOutinvstatusNumber(SalesOutBillEntry.OUTINVSTATUS_NORMAL);
entry.setOutownertype(SalesOutBillEntry.OUTOWNERTYPE_ORG);
entry.setOutownerNumber("1011");
entry.setOutkeepertype(SalesOutBillEntry.OUTKEEPERTYPE_ORG);
entry.setOutkeeperNumber("1011");
entry.setEntrysettleorgNumber("1011");
entry.setPrice(new BigDecimal("100.00"));
entry.setPriceandtax(new BigDecimal("113.00"));
entry.setTaxrateidNumber("V5");
entry.setTaxamount(new BigDecimal("65.00"));
entry.setAmountandtax(new BigDecimal("565.00"));
entry.setAmount(new BigDecimal("500.00"));
entry.setIspresent(false);
entry.setDiscounttype(SalesOutBillEntry.DISCOUNTTYPE_NONE);
entry.setEntrycomment("退货明细");
// 如果物料启用保质期管理，需要设置生产日期和到期日期
entry.setProducedate("2025-08-01");
entry.setExpirydate("2025-12-01");
// 如果物料启用批号管理，需要设置批号
entry.setLotnumber("BATCH-001");
entry.setGjwlProductLotno("PROD-BATCH-001");

entries.add(entry);
salesReturnBill.setBillentry(entries);
salesReturnBills.add(salesReturnBill);

request.setData(salesReturnBills);

// 执行请求
SalesOutBillSaveResponse response = request.execute(api);
response.check();

// 获取保存结果
SalesOutBillSaveResponseData data = response.getData();
if (data != null && data.getResult() != null && !data.getResult().isEmpty()) {
    SalesOutBillSaveResponseData.SalesOutBillSaveResult result = data.getResult().get(0);
    if (result.getBillStatus()) {
        System.out.println("销售退货单保存成功，单据ID: " + result.getId());
        System.out.println("单据编号: " + result.getNumber());
    }
}
```

### 项目管理示例

#### 项目保存提交审核
```java
ProjectSaveRequest request = new ProjectSaveRequest();

// 创建项目信息列表
List<Project> projects = new ArrayList<>();
Project project = new Project();
project.setNumber("Prj-000001");
project.setName("测试项目01");
project.setCreateorgNumber("gjwl");
project.setGjwlAppid("iIa9l");
project.setGjwlSourcesystemtype("广交云供应链管理系统");

projects.add(project);
request.setParam(projects);

// 执行请求
ProjectSaveResponse response = request.execute(api);
response.check();
```

#### 项目修改示例
```java
ProjectSaveRequest request = new ProjectSaveRequest();

// 创建项目信息列表（修改时需要传入ID）
List<Project> projects = new ArrayList<>();
Project project = new Project();
project.setId("2274786458764061696"); // 修改时需传入项目ID
project.setNumber("Prj-000001");
project.setName("修改后的项目名称");
project.setCreateorgNumber("gjwl");
project.setGjwlAppid("iIa9l");
project.setGjwlSourcesystemtype("广交云供应链管理系统");

projects.add(project);
request.setParam(projects);

// 执行请求
ProjectSaveResponse response = request.execute(api);
response.check();
```

#### 项目反审核示例
```java
ProjectUnAuditRequest request = new ProjectUnAuditRequest();

// 创建项目反审核参数
ProjectUnAudit unAudit = new ProjectUnAudit();
List<String> projectIds = new ArrayList<>();
projectIds.add("1402088445405483008");
projectIds.add("5764822583698205696");
unAudit.setId(projectIds);

request.setParam(unAudit);

// 执行请求
ProjectUnAuditResponse response = request.execute(api);
response.check();
```

#### 单个项目反审核示例
```java
ProjectUnAuditRequest request = new ProjectUnAuditRequest();

// 创建单个项目反审核参数
ProjectUnAudit unAudit = new ProjectUnAudit();
List<String> projectIds = new ArrayList<>();
projectIds.add("2274786458764061696");
unAudit.setId(projectIds);

request.setParam(unAudit);

// 执行请求
ProjectUnAuditResponse response = request.execute(api);
response.check();
```

### 物料管理示例

#### 物料批量保存提交审核
```java
MaterialSaveRequest request = new MaterialSaveRequest();

// 创建物料保存数据
MaterialSaveRequest.MaterialSaveData saveData = new MaterialSaveRequest.MaterialSaveData();
List<MaterialDataVo> materials = new ArrayList<>();

MaterialDataVo materialData = new MaterialDataVo();

// 设置物料主档信息
MaterialInfoVo materialInfo = new MaterialInfoVo();
materialInfo.setName("测试物料");
materialInfo.setNumber("MAT001");
materialInfo.setGjwlAppid("EXT001");
materialInfo.setCreateorg("gjwl");
materialInfo.setBaseunit("Pcs");
materialInfo.setModelnum("Model001");
materialInfo.setUnitconvertdir("A");

// 设置分类信息
List<GroupStandardEntry> groupStandards = new ArrayList<>();
GroupStandardEntry groupStandard = new GroupStandardEntry();
groupStandard.setGroupStrandardCreateOrg("gjwl");
groupStandard.setGroupStrandardNumber("JBFLBZ");
groupStandard.setGroupNumber("GROUP001");
groupStandards.add(groupStandard);
materialInfo.setEntryGroupstandard(groupStandards);

materialData.setMaterialInfoVo(materialInfo);

// 设置物料组织公共信息
CommonInfoVo commonInfo = new CommonInfoVo();
commonInfo.setMaterialtype("1");
commonInfo.setMaterialattr("10040");
commonInfo.setEnablepur(true);
commonInfo.setEnablesale(true);
commonInfo.setEnableinv(true);
commonInfo.setGroup("CHJZFL06-SYS");

materialData.setCommonInfoVo(commonInfo);
materials.add(materialData);

saveData.setMaterials(materials);
request.setParam(saveData);

MaterialSaveResponse response = request.execute(api);
response.check();
```

#### 物料库存信息批量更新
```java
MaterialInventoryUpdateRequest request = new MaterialInventoryUpdateRequest();

List<MaterialInventoryUpdatePara> updateList = new ArrayList<>();
MaterialInventoryUpdatePara updatePara = new MaterialInventoryUpdatePara();

updatePara.setEnableshelflifemgr(true);
updatePara.setShelflifeunit("day");
updatePara.setShelflife(90);
updatePara.setCaldirection("4");
updatePara.setStartdatecaltype("1");
updatePara.setCalculationforenddate("0");
updatePara.setLeadtimeunit("day");
updatePara.setMasteridNumber("MAT001");
updatePara.setCreateorgNumber("gjwl");

updateList.add(updatePara);
request.setParam(updateList);

MaterialInventoryUpdateResponse response = request.execute(api);
response.check();
```

### 库存管理示例

#### 盘盈单保存
```java
SurplusBillSaveRequest request = new SurplusBillSaveRequest();

// 创建盘盈单列表
List<SurplusBill> surplusBills = new ArrayList<>();
SurplusBill surplusBill = new SurplusBill();

// 设置盘盈单头信息
surplusBill.setOrgNumber("1011");
surplusBill.setBillno("PYRK-250814-000002");
surplusBill.setGjwlThirdpartyBillno("PYRK-250814-000002");
surplusBill.setBookdate("2025-08-14");
surplusBill.setBilltypeNumber("im_InvCheckIn_STD_BT_S");
surplusBill.setBiztypeNumber("350");
surplusBill.setBiztime("2025-08-14");
surplusBill.setInvschemeNumber("350");
surplusBill.setComment("盘盈单备注");

// 创建盘盈单明细
List<SurplusBillEntry> entries = new ArrayList<>();
SurplusBillEntry entry = new SurplusBillEntry();

entry.setLinetypeNumber("010");
entry.setMaterialNumber("Item-*********");
entry.setInvstatusNumber("110");
entry.setInvtypeNumber("110");
entry.setOwnerNumber("1011");
entry.setKeeperNumber("1011");
entry.setOwnertype("bos_org");
entry.setKeepertype("bos_org");
entry.setUnitNumber("pcs");
entry.setWarehouseNumber("CK-001");
entry.setBaseunitNumber("pcs");
entry.setProjectNumber("KD-P-ALL_SYS");
entry.setProducedate("2025-07-29");
entry.setExpirydate("2029-07-29");
entry.setQty(new BigDecimal("10"));
entry.setBaseqty(new BigDecimal("10"));
entry.setInvgainqty(new BigDecimal("10"));
entry.setLotnumber("123");
entry.setGjwlProductLotno("123");
entry.setEntrycomment("单据体备注");

entries.add(entry);
surplusBill.setBillentry(entries);
surplusBills.add(surplusBill);

request.setParam(surplusBills);

// 执行请求
SurplusBillSaveResponse response = request.execute(api);
response.check();
```

#### 盘亏单保存
```java
DeficitBillSaveRequest request = new DeficitBillSaveRequest();

// 创建盘亏单列表
List<DeficitBill> deficitBills = new ArrayList<>();
DeficitBill deficitBill = new DeficitBill();

// 设置盘亏单头信息
deficitBill.setOrgNumber("gjwl");
deficitBill.setBillno("PYRK-231012-000004");
deficitBill.setGjwlThirdpartyBillno("PYRK-231012-000004");
deficitBill.setBookdate("2024-10-14");
deficitBill.setBilltypeNumber("im_InvCheckOut_STD_BT_S");
deficitBill.setBiztypeNumber("351");
deficitBill.setBiztime("2024-10-14");
deficitBill.setInvschemeNumber("351");

// 创建盘亏单明细
List<DeficitBillEntry> entries = new ArrayList<>();
DeficitBillEntry entry = new DeficitBillEntry();

entry.setLinetypeNumber("010");
entry.setMaterialNumber("Item-00000001");
entry.setOutinvstatusNumber("110");
entry.setOutinvtypeNumber("110");
entry.setOutownerNumber("gjwl");
entry.setOutkeeperNumber("gjwl");
entry.setOutownertype("bos_org");
entry.setOutkeepertype("bos_org");
entry.setUnitNumber("g");
entry.setWarehouseNumber("CK-002");
entry.setBaseunitNumber("g");
entry.setQty(new BigDecimal("188.09"));
entry.setBaseqty(new BigDecimal("188.09"));
entry.setInvqtyacc(new BigDecimal("216.43"));
entry.setInvlossqty(new BigDecimal("166.25"));

entries.add(entry);
deficitBill.setBillentry(entries);
deficitBills.add(deficitBill);

request.setParam(deficitBills);

// 执行请求
DeficitBillSaveResponse response = request.execute(api);
response.check();
```

#### 其他出库单保存
```java
OtherOutBillSaveRequest request = new OtherOutBillSaveRequest();

// 创建其他出库单列表
List<OtherOutBill> otherOutBills = new ArrayList<>();
OtherOutBill otherOutBill = new OtherOutBill();

// 设置其他出库单头信息
otherOutBill.setOrgNumber("1011");
otherOutBill.setBillno("QTCK-250822-000002");
otherOutBill.setGjwlThirdpartyBillno("QTCK-250822-000002");
otherOutBill.setGjwlSourcesystemtype("广交云供应链管理系统");
otherOutBill.setBiztime("2025-08-22");
otherOutBill.setBookdate("2025-08-22");
otherOutBill.setBilltypeNumber("im_OtherOutBill_STD_BT_S");
otherOutBill.setBiztypeNumber("355");
otherOutBill.setInvschemeNumber("355");
otherOutBill.setBizdeptNumber("1011EY11EY1102");
otherOutBill.setComment("备注");

// 创建其他出库单明细
List<OtherOutBillEntry> entries = new ArrayList<>();
OtherOutBillEntry entry = new OtherOutBillEntry();

entry.setLinetypeNumber("010");
entry.setMaterialNumber("Item-*********");
entry.setUnitNumber("pcs");
entry.setQty(new BigDecimal("10"));
entry.setWarehouseNumber("CK-001");
entry.setOutinvtypeNumber("110");
entry.setOutinvstatusNumber("110");
entry.setOutownertype("bos_org");
entry.setOutownerNumber("1011");
entry.setOutkeepertype("bos_org");
entry.setOutkeeperNumber("1011");
entry.setProjectNumber("KD-P-ALL_SYS");
entry.setLotnumber("123");
entry.setGjwlProductLotno("123");
entry.setProducedate("2025-07-29");
entry.setExpirydate("2029-07-29");
entry.setInvtypeNumber("110");
entry.setInvstatusNumber("110");
entry.setOwnertype("bos_org");
entry.setOwnerNumber("1011");
entry.setKeepertype("bos_org");
entry.setKeeperNumber("1011");
entry.setPrice(new BigDecimal("310.63"));
entry.setEntrycomment("分录备注");

entries.add(entry);
otherOutBill.setBillentry(entries);
otherOutBills.add(otherOutBill);

request.setParam(otherOutBills);

// 执行请求
OtherOutBillSaveResponse response = request.execute(api);
response.check();
```

#### 分步调出单保存
```java
TransOutBillSaveRequest request = new TransOutBillSaveRequest();

// 创建分步调出单列表
List<TransOutBill> transOutBills = new ArrayList<>();
TransOutBill transOutBill = new TransOutBill();

// 设置分步调出单头信息
transOutBill.setBillno("DBCK-250822-000001");
transOutBill.setGjwlThirdpartyBillno("DBCK-250822-000001");
transOutBill.setGjwlSourcesystemtype("广交云供应链管理系统");
transOutBill.setBiztime("2025-08-22");
transOutBill.setComment("备注");
transOutBill.setTranstype("A");
transOutBill.setOrgNumber("1011");
transOutBill.setBilltypeNumber("im_AllotOutBill_STD_BT_S");
transOutBill.setBiztypeNumber("310");
transOutBill.setInvschemeNumber("315");
transOutBill.setInorgNumber("1011");
transOutBill.setSettlescurrencyNumber("CNY");

// 创建分步调出单明细
List<TransOutBillEntry> entries = new ArrayList<>();
TransOutBillEntry entry = new TransOutBillEntry();

entry.setLinetypeNumber("010");
entry.setMaterialNumber("Item-*********");
entry.setUnitNumber("pcs");
entry.setQty(new BigDecimal("20"));
entry.setWarehouseNumber("CK-001");
entry.setLotnumber("123");
entry.setGjwlProductLotno("123");
entry.setOwnerNumber("1011");
entry.setProducedate("2025-07-29");
entry.setExpirydate("2029-07-29");
entry.setOutinvstatusNumber("110");
entry.setOutinvtypeNumber("110");
entry.setOutownertype("bos_org");
entry.setOutownerNumber("1011");
entry.setOutkeepertype("bos_org");
entry.setOutkeeperNumber("1011");
entry.setProjectNumber("KD-P-ALL_SYS");
entry.setInprojectNumber("KD-P-ALL_SYS");
entry.setIsfreegift(false);
entry.setEntrycomment("单据体备注");
entry.setInwarehouseNumber("CK-005");
entry.setOwnertype("bos_org");
entry.setKeepertype("bos_org");
entry.setKeeperNumber("1011");
entry.setInvstatusNumber("114");
entry.setInvtypeNumber("110");

entries.add(entry);
transOutBill.setBillentry(entries);
transOutBills.add(transOutBill);

request.setParam(transOutBills);

// 执行请求
TransOutBillSaveResponse response = request.execute(api);
response.check();
```

#### 分步调入单保存
```java
TransInBillSaveRequest request = new TransInBillSaveRequest();

// 创建分步调入单列表
List<TransInBill> transInBills = new ArrayList<>();
TransInBill transInBill = new TransInBill();

// 设置分步调入单头信息
transInBill.setOrgNumber("BU-001");
transInBill.setBillno("DBRK-250731-000001");
transInBill.setGjwlThirdpartyBillno("DBRK-250731-000001");
transInBill.setGjwlSourcesystemtype("广交云供应链管理系统");
transInBill.setBiztime("2025-07-31");
transInBill.setBilltypeNumber("im_AllotInBill_STD_BT_S");
transInBill.setBiztypeNumber("310");
transInBill.setInvschemeNumber("316");
transInBill.setTranstype("A");
transInBill.setTransit("A");
transInBill.setOutorgNumber("BU-002");
transInBill.setSettlescurrencyNumber("CNY");
transInBill.setComment("备注");

// 创建分步调入单明细
List<TransInBillEntry> entries = new ArrayList<>();
TransInBillEntry entry = new TransInBillEntry();

entry.setLinetypeNumber("010");
entry.setMaterialNumber("Item-*********");
entry.setUnitNumber("pcs");
entry.setQty(new BigDecimal("10"));
entry.setWarehouseNumber("CK-001");
entry.setInvstatusNumber("110");
entry.setOwnertype("bos_org");
entry.setOwnerNumber("BU-001");
entry.setKeepertype("bos_org");
entry.setKeeperNumber("BU-001");
entry.setProducedate("2025-07-29");
entry.setExpirydate("2029-07-29");
entry.setInvtypeNumber("110");
entry.setOutinvstatusNumber("114");
entry.setOutinvtypeNumber("110");
entry.setOutownertype("bos_org");
entry.setOutkeepertype("bos_org");
entry.setOutownerNumber("BU-002");
entry.setOutkeeperNumber("BU-002");
entry.setOutwarehouseNumber("CK-002");

entries.add(entry);
transInBill.setBillentry(entries);
transInBills.add(transInBill);

request.setParam(transInBills);

// 执行请求
TransInBillSaveResponse response = request.execute(api);
response.check();
```

## 异常处理
  
### 常见异常类型  
```java  
try {  
    response = request.execute(api);    response.check();} catch (KingDeeOpException e) {  
    switch (e.getCode()) {        case TOKEN_CHECK_SIGN_ERROR:            // 处理认证错误  
            break;        case PARAMETER_CHECK_ERROR:            // 处理参数错误  
            break;        case API_ERROR:            // 处理API错误  
            break;        default:            // 处理其他错误  
            break;    }} catch (ServiceException e) {  
    // 处理服务异常  
    log.error("服务异常: {}", e.getMessage());  
}  
```  
  
## 配置说明  
  
### HTTP客户端配置  
- 连接超时：60秒  
- 读取超时：60秒  - 写入超时：60秒  
  
### JSON序列化配置  
- 忽略null值字段  
- 日期格式：yyyy-MM-dd HH:mm:ss  
- 日期格式：yyyy-MM-dd  
- 忽略未知属性  
  
### 日志配置  
- 自动记录请求和响应内容  
- 支持自定义日志键值  
- 异常信息自动记录

### 下推并保存管理示例

#### 销售订单下推销售出库单
```java
// 创建下推并保存请求
PushAndSaveRequest request = new PushAndSaveRequest();

// 创建请求参数
PushAndSaveRequestParam param = new PushAndSaveRequestParam();
param.setSourceEntityNumber("sm_salorder"); // 源单：销售订单
param.setTargetEntityNumber("im_saloutbill"); // 目标单：销售出库单
param.setRuleId("610056021305574400"); // 转换规则ID

// 创建源单信息
List<SelectedBill> sourceBills = new ArrayList<>();
SelectedBill sourceBill = new SelectedBill();
sourceBill.setBillId("123456789"); // 源单ID
// sourceBill.setEntryIds(Arrays.asList("entry1", "entry2")); // 可选：指定分录ID，为空则整单下推
sourceBills.add(sourceBill);

param.setSourceBills(sourceBills);
request.setParam(param);

// 执行请求
PushAndSaveResponse response = request.execute(api);
response.check();

// 处理响应结果
PushApiResult data = response.getData();
if (data != null && data.getResult() != null) {
    PushResult pushResult = data.getResult().getPushResult();
    if (pushResult != null) {
        System.out.println("下推成功: " + pushResult.getPushSuccess());
        System.out.println("保存成功: " + pushResult.getSaveSuccess());

        if (pushResult.getTargetBills() != null) {
            for (TargetBill targetBill : pushResult.getTargetBills()) {
                System.out.println("目标单ID: " + targetBill.getBillId());
                System.out.println("目标单编号: " + targetBill.getBillNo());
            }
        }
    }

    // 获取目标单据详细信息
    if (data.getResult().getTargetResult() != null) {
        for (TargetResult targetResult : data.getResult().getTargetResult()) {
            System.out.println("目标单据ID: " + targetResult.getFid());
            System.out.println("目标单据编号: " + targetResult.getFbillno());
            System.out.println("物料编码: " + targetResult.getFnumber());
        }
    }
}
```

#### 采购订单下推采购入库单
```java
PushAndSaveRequest request = new PushAndSaveRequest();

PushAndSaveRequestParam param = new PushAndSaveRequestParam();
param.setSourceEntityNumber("pm_purorderbill"); // 源单：采购订单
param.setTargetEntityNumber("im_purinbill"); // 目标单：采购入库单
param.setRuleId("565033700123759616"); // 转换规则ID

List<SelectedBill> sourceBills = new ArrayList<>();
SelectedBill sourceBill = new SelectedBill();
sourceBill.setBillId("*********"); // 源单ID
sourceBills.add(sourceBill);

param.setSourceBills(sourceBills);
request.setParam(param);

PushAndSaveResponse response = request.execute(api);
response.check();
```

#### 分步调出单下推分步调入单
```java
PushAndSaveRequest request = new PushAndSaveRequest();

PushAndSaveRequestParam param = new PushAndSaveRequestParam();
param.setSourceEntityNumber("im_transoutbill"); // 源单：分步调出单
param.setTargetEntityNumber("im_transinbill"); // 目标单：分步调入单
param.setRuleId("475190152356975616"); // 转换规则ID

List<SelectedBill> sourceBills = new ArrayList<>();
SelectedBill sourceBill = new SelectedBill();
sourceBill.setBillId("456789123"); // 源单ID
sourceBills.add(sourceBill);

param.setSourceBills(sourceBills);
request.setParam(param);

PushAndSaveResponse response = request.execute(api);
response.check();
```

#### 按分录下推示例
```java
PushAndSaveRequest request = new PushAndSaveRequest();

PushAndSaveRequestParam param = new PushAndSaveRequestParam();
param.setSourceEntityNumber("sm_salorder");
param.setTargetEntityNumber("im_saloutbill");
param.setRuleId("610056021305574400");

// 指定特定分录进行下推
List<SelectedBill> sourceBills = new ArrayList<>();
SelectedBill sourceBill = new SelectedBill();
sourceBill.setBillId("123456789");
sourceBill.setEntryIds(Arrays.asList("entry001", "entry002")); // 指定分录ID
sourceBills.add(sourceBill);

param.setSourceBills(sourceBills);
request.setParam(param);

PushAndSaveResponse response = request.execute(api);
response.check();
```

### 11. 附件上传接口使用示例

#### 方式一：从本地文件上传

```java
// 创建附件上传请求
AttachmentUploadRequest request = new AttachmentUploadRequest();

// 设置附件上传参数
AttachmentUploadFileArgs args = new AttachmentUploadFileArgs();
args.setEntityNumber("im_otheroutbill"); // 源实体标识
args.setBillPkId("2290610299448910848"); // 单据主键ID
args.setControlKey("attachmentpanel"); // 附件控件标识
args.setDescription("测试上传其他出库单附件"); // 附件备注

// 从本地文件创建请求参数
File file = new File("/path/to/file.jpg");
AttachmentUploadRequestParam param = AttachmentUploadHelper.createRequest(args, file);

request.setParam(param);

// 执行上传
AttachmentUploadResponse response = request.execute(api);

// 获取上传结果
AttachmentUploadFileResult result = response.getData();
String filePath = result.getPath(); // 获取文件路径
System.out.println("文件上传成功，路径：" + filePath);
```

#### 方式二：从字节数组上传

```java
// 创建附件上传请求
AttachmentUploadRequest request = new AttachmentUploadRequest();

// 设置附件上传参数
AttachmentUploadFileArgs args = new AttachmentUploadFileArgs();
args.setEntityNumber("im_otheroutbill");
args.setBillPkId("2290610299448910848");
args.setControlKey("attachmentpanel");
args.setDescription("测试上传其他出库单附件");

// 从字节数组创建请求参数
byte[] fileBytes = getFileBytes(); // 获取文件字节数组的方法
AttachmentUploadRequestParam param = AttachmentUploadHelper.createRequest(args, fileBytes, "test.jpg");

// 或者指定内容类型
// AttachmentUploadRequestParam param = AttachmentUploadHelper.createRequest(args, fileBytes, "test.jpg", "image/jpeg");

request.setParam(param);

// 可选：设置日志客户端
// SdkLogClient logClient = new SdkLogClientImpl();
// request.sdkLogClient(logClient);

// 执行上传
AttachmentUploadResponse response = request.execute(api);

// 获取上传结果
AttachmentUploadFileResult result = response.getData();
String filePath = result.getPath();
System.out.println("文件上传成功，路径：" + filePath);
```

**注意**：
1. AttachmentUploadRequest不继承KdOpRequest，直接实现了multipart/form-data文件上传逻辑
2. 支持从File对象或byte[]数组上传文件
3. 保留了日志记录、异常处理等核心功能
4. 自动根据文件扩展名推断内容类型

#### 不同单据类型的附件上传示例

```java
// 盘亏单附件上传
args.setEntityNumber("im_deficitbill");
args.setBillPkId("123456789");
args.setControlKey("attachmentpanel");

// 付款申请单附件上传
args.setEntityNumber("ap_payapply");
args.setBillPkId("*********");
args.setControlKey("attachmentpanel");

// 指定单据体行的附件上传
args.setEntityNumber("im_otheroutbill");
args.setBillPkId("2290610299448910848");
args.setEntryPkId("1957409102693941249"); // 单据体行主键
args.setControlKey("FaekA"); // 特定控件标识
```

## 注意事项
  
1. **线程安全**：KingdeeDefaultOpClient使用单例模式，线程安全  
2. **幂等性**：每次请求自动生成UUID作为幂等性键值  
3. **重试机制**：SDK本身不提供重试，需要业务层实现  
4. **超时处理**：建议设置合理的超时时间  
5. **日志记录**：所有请求都会记录详细日志，便于问题排查  
6. **错误处理**：务必调用response.check()检查业务响应码